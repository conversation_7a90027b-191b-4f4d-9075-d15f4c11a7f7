package com.tem.customer.service.system;

import com.tem.imgserver.client.ImgClient;
import com.tem.imgserver.client.UploadResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.InputStream;

/**
 * ImgClient包装类
 * 提供更好的异常处理和测试支持
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Component
public class ImgClientWrapper {

    /**
     * 上传图片到图片服务器
     * 
     * @param inputStream 文件输入流
     * @param bizType 业务类型
     * @param fileName 文件名
     * @return 上传结果
     * @throws RuntimeException 上传失败时抛出
     */
    public UploadResult uploadImg2(InputStream inputStream, String bizType, String fileName) {
        try {
            log.debug("开始上传图片，业务类型: {}, 文件名: {}", bizType, fileName);
            
            UploadResult result = ImgClient.uploadImg2(inputStream, bizType, fileName);
            
            if (result == null) {
                throw new RuntimeException("图片服务器返回空结果");
            }
            
            if (result.getFileKey() == null || result.getFileKey().trim().isEmpty()) {
                throw new RuntimeException("图片服务器返回的文件Key为空");
            }
            
            log.debug("图片上传成功，文件Key: {}", result.getFileKey());
            return result;
            
        } catch (Exception e) {
            log.error("图片上传失败，业务类型: {}, 文件名: {}, 错误: {}", bizType, fileName, e.getMessage(), e);
            
            // 包装异常，提供更详细的错误信息
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            } else {
                throw new RuntimeException("图片上传服务异常: " + e.getMessage(), e);
            }
        }
    }

    /**
     * 检查图片服务是否可用
     * 
     * @return true如果服务可用，false否则
     */
    public boolean isServiceAvailable() {
        try {
            // 这里可以添加健康检查逻辑
            // 例如调用图片服务的健康检查接口
            return true;
        } catch (Exception e) {
            log.warn("图片服务健康检查失败: {}", e.getMessage());
            return false;
        }
    }
}
